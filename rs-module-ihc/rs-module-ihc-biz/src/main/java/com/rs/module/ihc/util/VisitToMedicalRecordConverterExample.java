package com.rs.module.ihc.util;

import com.rs.module.ihc.convert.VisitToMedicalRecordConverter;
import com.rs.module.ihc.entity.ipm.VisitDO;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * VisitDO 转 MedicalRecordDO 转换器使用示例
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class VisitToMedicalRecordConverterExample {

    /**
     * 基础转换示例
     */
    public void basicConversionExample() {
        log.info("=== 基础转换示例 ===");

        // 创建测试数据
        VisitDO visitDO = createTestVisitDO();

        // 使用基础转换器
        MedicalRecordDO medicalRecordDO = VisitToMedicalRecordConverter.INSTANCE.visitToMedicalRecord(visitDO);

        // 输出转换结果
        log.info("转换前 - 巡诊记录：");
        log.info("  监管人员编码：{}", visitDO.getJgrybm());
        log.info("  在押人员名称：{}", visitDO.getRyxm());
        log.info("  主诉：{}", visitDO.getMainComplaint());
        log.info("  巡诊结论：{}", visitDO.getVisitConclusion());
        log.info("  巡诊人证件号码：{}", visitDO.getVisitUserid());
        log.info("  巡诊人名称：{}", visitDO.getVisitUserName());

        log.info("转换后 - 病历记录：");
        log.info("  监管人员编码：{}", medicalRecordDO.getJgrybm());
        log.info("  监管人员姓名：{}", medicalRecordDO.getJgryxm());
        log.info("  主诉：{}", medicalRecordDO.getMainComplaint());
        log.info("  诊断结论：{}", medicalRecordDO.getDiagnosisResult());
        log.info("  医生身份证号：{}", medicalRecordDO.getDoctorIdCard());
        log.info("  医生姓名：{}", medicalRecordDO.getDoctorName());
    }

    /**
     * 高级转换示例
     */
    public void advancedConversionExample() {
        log.info("=== 高级转换示例 ===");

        // 创建测试数据
        VisitDO visitDO = createTestVisitDO();
        visitDO.setDiseaseReason("头痛、发热");

        // 使用高级转换器
        MedicalRecordDO medicalRecordDO = AdvancedVisitToMedicalRecordConverter.INSTANCE.visitToMedicalRecordAdvanced(visitDO);

        log.info("高级转换结果：");
        log.info("  病史：{}", medicalRecordDO.getMedicalHistory());
    }

    /**
     * 批量转换示例
     */
    public void batchConversionExample() {
        log.info("=== 批量转换示例 ===");

        // 创建测试数据列表
        List<VisitDO> visitDOList = Arrays.asList(
                createTestVisitDO("001", "张三"),
                createTestVisitDO("002", "李四"),
                createTestVisitDO("003", "王五")
        );

        // 批量转换
        List<MedicalRecordDO> medicalRecordDOList = AdvancedVisitToMedicalRecordConverter.INSTANCE
                .visitListToMedicalRecordList(visitDOList);

        log.info("批量转换完成，共转换 {} 条记录", medicalRecordDOList.size());
        medicalRecordDOList.forEach(record ->
                log.info("  监管人员：{} - {}", record.getJgrybm(), record.getJgryxm()));
    }

    /**
     * 更新现有记录示例
     */
    public void updateExistingRecordExample() {
        log.info("=== 更新现有记录示例 ===");

        // 创建现有的病历记录
        MedicalRecordDO existingRecord = new MedicalRecordDO();
        existingRecord.setId("existing-id");
        existingRecord.setJgrybm("001");
        existingRecord.setJgryxm("张三");
        existingRecord.setPastMedicalHistory("既往有高血压病史");

        // 创建新的巡诊记录
        VisitDO newVisitDO = createTestVisitDO();
        newVisitDO.setMainComplaint("胸闷气短");
        newVisitDO.setVisitConclusion("建议进一步检查");

        log.info("更新前 - 病历记录主诉：{}", existingRecord.getMainComplaint());
        log.info("更新前 - 病历记录诊断结论：{}", existingRecord.getDiagnosisResult());

        // 更新现有记录
        AdvancedVisitToMedicalRecordConverter.INSTANCE.updateMedicalRecordFromVisit(newVisitDO, existingRecord);

        log.info("更新后 - 病历记录主诉：{}", existingRecord.getMainComplaint());
        log.info("更新后 - 病历记录诊断结论：{}", existingRecord.getDiagnosisResult());
        log.info("ID 保持不变：{}", existingRecord.getId());
        log.info("既往史保持不变：{}", existingRecord.getPastMedicalHistory());
    }

    /**
     * 创建测试用的 VisitDO 对象
     */
    private VisitDO createTestVisitDO() {
        return createTestVisitDO("001", "张三");
    }

    /**
     * 创建测试用的 VisitDO 对象
     */
    private VisitDO createTestVisitDO(String jgrybm, String ryxm) {
        VisitDO visitDO = new VisitDO();
        visitDO.setId("visit-" + jgrybm);
        visitDO.setJgrybm(jgrybm);
        visitDO.setRyxm(ryxm);
        visitDO.setMainComplaint("头痛、乏力");
        visitDO.setVisitConclusion("建议休息，多饮水");
        visitDO.setVisitUserid("110101199001011234");
        visitDO.setVisitUserName("李医生");
        visitDO.setVisitTime(new Date());
        visitDO.setDiseaseReason("感冒症状");
        return visitDO;
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        basicConversionExample();
        advancedConversionExample();
        batchConversionExample();
        updateExistingRecordExample();
    }
}
