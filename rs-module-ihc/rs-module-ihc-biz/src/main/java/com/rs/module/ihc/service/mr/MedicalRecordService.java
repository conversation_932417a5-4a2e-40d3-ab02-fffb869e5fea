package com.rs.module.ihc.service.mr;

import java.util.*;
import javax.validation.*;
import com.rs.module.ihc.controller.admin.mr.vo.*;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 病历管理 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicalRecordService extends IBaseService<MedicalRecordDO>{

    /**
     * 创建病历管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMedicalRecord(@Valid MedicalRecordSaveReqVO createReqVO);

    /**
     * 更新病历管理
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicalRecord(@Valid MedicalRecordSaveReqVO updateReqVO);

    /**
     * 删除病历管理
     *
     * @param id 编号
     */
    void deleteMedicalRecord(String id);

    /**
     * 获得病历管理
     *
     * @param id 编号
     * @return 病历管理
     */
    MedicalRecordDO getMedicalRecord(String id);

    /**
    * 获得病历管理分页
    *
    * @param pageReqVO 分页查询
    * @return 病历管理分页
    */
    PageResult<MedicalRecordDO> getMedicalRecordPage(MedicalRecordPageReqVO pageReqVO);

    /**
    * 获得病历管理列表
    *
    * @param listReqVO 查询条件
    * @return 病历管理列表
    */
    List<MedicalRecordDO> getMedicalRecordList(MedicalRecordListReqVO listReqVO);


}
