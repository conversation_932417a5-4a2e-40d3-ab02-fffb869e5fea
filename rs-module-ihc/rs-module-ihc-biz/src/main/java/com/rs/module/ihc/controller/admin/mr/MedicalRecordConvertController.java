package com.rs.module.ihc.controller.admin.mr;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.ihc.entity.ipm.VisitDO;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import com.rs.module.ihc.service.mr.MedicalRecordConvertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 病历转换控制器
 * 
 * <AUTHOR>
 */
@Api(tags = "管理后台 - 病历转换")
@RestController
@RequestMapping("/admin/ihc/medical-record-convert")
@Slf4j
public class MedicalRecordConvertController {

    @Autowired
    private MedicalRecordConvertService medicalRecordConvertService;

    @PostMapping("/visit-to-medical-record")
    @ApiOperation("将巡诊记录转换为病历记录")
    public CommonResult<MedicalRecordDO> convertVisitToMedicalRecord(
            @ApiParam("巡诊记录") @Valid @RequestBody VisitDO visitDO) {
        return medicalRecordConvertService.convertVisitToMedicalRecord(visitDO);
    }

    @PostMapping("/visit-list-to-medical-record-list")
    @ApiOperation("批量将巡诊记录转换为病历记录")
    public CommonResult<List<MedicalRecordDO>> convertVisitListToMedicalRecordList(
            @ApiParam("巡诊记录列表") @Valid @RequestBody List<VisitDO> visitDOList) {
        return medicalRecordConvertService.convertVisitListToMedicalRecordList(visitDOList);
    }
}
